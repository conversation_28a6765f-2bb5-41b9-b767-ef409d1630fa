import { useState, useEffect } from "react";
import { useUser } from "@/context/UserContext";

export const useProfilePicture = () => {
  const { user, updateUser } = useUser();
  const [profilePicture, setProfilePicture] = useState(null);
  const [previewUrl, setPreviewUrl] = useState(null);
  const [isUploading, setIsUploading] = useState(false);

  const MAX_SIZE_BYTES = 1 * 1024 * 1024; // 1MB
  const ALLOWED_TYPES = ["image/jpeg", "image/jpg", "image/png", "image/gif"];

  // Initialize preview URL from user data
  useEffect(() => {
    if (user?.avatar_path) {
      setPreviewUrl(user.avatar_path);
    }
  }, [user?.avatar_path]);

  // Handle file selection
  const handleFileSelect = (file) => {
    if (!file) {
      clearSelection();
      return { success: false, error: "No file selected" };
    }

    // Type validation
    if (!ALLOWED_TYPES.includes(file.type)) {
      clearSelection();
      return { 
        success: false, 
        error: "Please select a valid image file (JPG, PNG, or GIF)" 
      };
    }

    // Size validation
    if (file.size > MAX_SIZE_BYTES) {
      clearSelection();
      return { 
        success: false, 
        error: "Image is too large. Maximum size is 1MB" 
      };
    }

    setProfilePicture(file);
    return { success: true };
  };

  // Handle file input change
  const handleFileChange = (event) => {
    const file = event.target.files?.[0];
    const result = handleFileSelect(file);
    
    // Clear the input value to allow selecting the same file again
    if (event.target) {
      event.target.value = "";
    }
    
    return result;
  };

  // Clear selection
  const clearSelection = () => {
    setProfilePicture(null);
    // Reset preview to user's current avatar
    setPreviewUrl(user?.avatar_path || null);
  };

  // Update profile picture in context after successful upload
  const updateProfilePicture = (newAvatarPath) => {
    updateUser({ avatar_path: newAvatarPath });
    setPreviewUrl(newAvatarPath);
    setProfilePicture(null);
  };

  // Create preview URL for selected file
  useEffect(() => {
    if (!profilePicture) return;

    const url = URL.createObjectURL(profilePicture);
    setPreviewUrl(url);

    return () => URL.revokeObjectURL(url);
  }, [profilePicture]);

  return {
    profilePicture,
    previewUrl,
    isUploading,
    setIsUploading,
    handleFileSelect,
    handleFileChange,
    clearSelection,
    updateProfilePicture,
    maxSize: MAX_SIZE_BYTES,
    allowedTypes: ALLOWED_TYPES,
  };
};
