"use client";

import React, { useState, useRef, useEffect } from "react";
import Link from "next/link";
import {
  ArrowLeftIcon,
  PaperAirplaneIcon,
  PaperClipIcon,
  InformationCircleIcon,
  UserCircleIcon,
  CheckIcon,
  ClockIcon,
  FunnelIcon,
  PlusIcon,
} from "@heroicons/react/24/outline";
import ApiPath from "@/network/api/api_path";
import NetworkService from "@/network/service/network_service";
import { toast } from "react-toastify";
import Image from "next/image";

function SupportChat({ ticketUID }) {
  const networkService = new NetworkService();
  const [loading, setLoading] = useState(true);

  const [messages, setMessages] = useState(null);

  // reply message
  const [attachments, setAttachments] = useState([]);
  const [replyMessage, setReplyMessage] = useState("");

  const fileInputRef = useRef(null);
  const messagesEndRef = useRef(null);

  // Scroll to bottom
  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
  };

  // Add selected files to attachment list
  const handleFileUpload = (e) => {
    const files = Array.from(e.target.files);
    setAttachments((prev) => [...prev, ...files]);
    e.target.value = null;
  };

  // Remove attachment
  const removeAttachment = (index) => {
    setAttachments((prev) => prev.filter((_, i) => i !== index));
  };

  // fetch ticket and messages
  const fetchTicket = async () => {
    setLoading(true);
    try {
      const res = await networkService.get(
        ApiPath.getSupportTicketChat(ticketUID)
      );
      if (res.status === "completed") {
        setMessages(res.data.data);
      }
    } finally {
      setLoading(false);
    }
  };

  // reply post message
  const replyPostMessage = async () => {
    if (!replyMessage.trim()) {
      toast.error("Message cannot be empty!");
      return;
    }
    try {
      const formData = new FormData();
      formData.append("message", replyMessage);
      attachments.forEach((file) => {
        formData.append("attachments[]", file);
      });
      const res = await networkService.postFormData(
        ApiPath.replySupportTicket(ticketUID),
        formData
      );
      if (res.status === "completed") {
        setReplyMessage("");
        setAttachments([]);
        fetchTicket();
        toast.success(res.data.message);
      }
    } finally {
    }
  };

  useEffect(() => {
    fetchTicket();
  }, []);

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  // message status
  const getMessageStatus = (status) => {
    switch (status) {
      case "sending":
        return <ClockIcon className="w-3 h-3 text-gray-400" />;
      case "delivered":
        return <CheckIcon className="w-3 h-3 text-blue-500" />;
      case "read":
        return <CheckIcon className="w-3 h-3 text-green-500" />;
      default:
        return null;
    }
  };

  return (
    <div className="h-screen flex flex-col">
      <div className="flex items-center justify-between mb-2">
        <div className="flex items-center space-x-4">
          <Link
            href="/support/tickets"
            className="inline-flex items-center text-sm text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200"
          >
            <ArrowLeftIcon className="w-4 h-4 mr-1" />
            Back to Tickets
          </Link>
        </div>
      </div>

      {/* Header */}
      <div className="bg-white dark:bg-gray-800 border-b rounded-t-lg border-gray-200 dark:border-gray-700 px-6 py-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <div className="relative">
              <div className="w-10 h-10 bg-blue-500 rounded-full flex items-center justify-center">
                <UserCircleIcon className="w-6 h-6 text-white" />
              </div>
              <div className="absolute bottom-0 right-0 w-3 h-3 bg-green-400 border-2 border-white dark:border-gray-800 rounded-full"></div>
            </div>
            <div>
              <h3 className="text-sm font-semibold text-gray-900 dark:text-white">
                {messages?.ticket?.user?.name}
              </h3>
              <p className="text-sm text-gray-500 dark:text-gray-400">
                {messages?.ticket?.user?.email}
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Body */}
      {loading ? (
        <div className="p-6 text-center text-gray-500">
          <p className="mt-2">Loading ticket...</p>
        </div>
      ) : messages?.messages?.length > 0 ? (
        <>
          {/* Ticket Info Banner */}
          <div className="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 px-6 py-3">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-2">
                <InformationCircleIcon className="w-4 h-4 text-blue-600" />
                <span className="text-sm text-blue-800 dark:text-blue-200">
                  Ticket # {messages?.ticket?.id}
                </span>
              </div>
              <div className="flex items-center space-x-2">
                <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200">
                  {messages?.ticket?.status}
                </span>
                <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-200">
                  {messages?.ticket?.priority}
                </span>
              </div>
            </div>
          </div>

          {/* Messages */}
          <div className="flex-1 overflow-y-auto bg-gray-100 dark:bg-gray-900 p-6">
            <div className="space-y-4">
              {/* First message */}
              <div className="flex flex-col mr-auto gap-2">
                <div className="flex justify-end items-center space-x-3">
                  <div className="relative">
                    <div className="w-10 h-10 bg-blue-500 rounded-full flex items-center justify-center">
                      <Image
                        src={
                          messages?.ticket?.user?.avatar ||
                          "/images/user/owner.jpg"
                        }
                        alt="user avatar"
                        width={100}
                        height={100}
                        className="w-full h-full object-cover rounded-full"
                      />
                    </div>
                  </div>
                  <div>
                    <h1 className="text-sm font-semibold text-gray-900 dark:text-white">
                      {messages?.ticket?.user?.name}
                    </h1>
                    <p className="text-xs text-gray-500 dark:text-gray-400">
                      {messages?.ticket?.user?.email}
                    </p>
                  </div>
                </div>
                <div className=" flex justify-end space-x-2">
                  <div className="mr-2">
                    <div className="px-4 py-2 rounded-lg bg-blue-600 text-white">
                      <p className="text-sm">{messages?.ticket?.message}</p>
                    </div>
                    <div className="flex items-center mt-1 space-x-1 justify-end">
                      <span className="text-xs text-gray-500 dark:text-gray-400">
                        {messages?.ticket?.updated_at}
                      </span>
                    </div>
                  </div>
                </div>
              </div>

              {/* Chat messages */}
              {messages?.messages?.map((msg) => {
                const sender = msg.is_admin ? "support" : "user";
                return (
                  <div
                    key={msg.id}
                    className={`flex ${
                      sender === "user" ? "justify-end" : "justify-start"
                    }`}
                  >
                    <div
                      className={`flex max-w-xs lg:max-w-md ${
                        sender === "user" ? "flex-row-reverse" : "flex-row"
                      } space-x-2`}
                    >
                      {sender === "support" && (
                        <img
                          src={msg.user.avatar}
                          alt={msg.user.name}
                          className="w-8 h-8 rounded-full flex-shrink-0"
                        />
                      )}
                      <div className={`${sender === "user" ? "mr-2" : "ml-2"}`}>
                        <div
                          className={`px-4 py-2 rounded-lg ${
                            sender === "user"
                              ? "bg-blue-600 text-white"
                              : "bg-white dark:bg-gray-800 text-gray-900 dark:text-white border border-gray-200 dark:border-gray-700"
                          }`}
                        >
                          <p className="text-sm">{msg.message}</p>
                        </div>
                        <div
                          className={`flex items-center mt-1 space-x-1 ${
                            sender === "user" ? "justify-end" : "justify-start"
                          }`}
                        >
                          <span className="text-xs text-gray-500 dark:text-gray-400">
                            {msg.created_at_formatted}
                          </span>
                          {sender === "user" && getMessageStatus?.(msg.status)}
                        </div>
                      </div>
                    </div>
                  </div>
                );
              })}
            </div>
            <div ref={messagesEndRef} />
          </div>
        </>
      ) : (
        <div className="p-12 text-center">
          <div className="mx-auto w-20 h-20 bg-gray-100 dark:bg-gray-700 rounded-full flex items-center justify-center mb-4">
            <FunnelIcon className="w-8 h-8 text-gray-400" />
          </div>
          <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
            No messages found
          </h3>
          <p className="text-gray-500 dark:text-gray-400 mb-6">
            This ticket has no messages. You can start the conversation by
            typing a message below.
          </p>
        </div>
      )}

      {/* Input with attachments */}
      <div className="bg-white dark:bg-gray-800 border-t rounded-b-lg border-gray-200 dark:border-gray-700 p-4">
        {attachments.length > 0 && (
          <div className="flex flex-wrap gap-2 mb-2">
            {attachments.map((file, index) => (
              <div
                key={index}
                className="flex items-center bg-gray-100 dark:bg-gray-700 px-3 py-1 rounded-lg"
              >
                <span className="text-sm text-gray-700 dark:text-gray-200">
                  {file.name}
                </span>
                <button
                  type="button"
                  onClick={() => removeAttachment(index)}
                  className="ml-2 text-gray-400 hover:text-red-500 dark:hover:text-red-400"
                >
                  &times;
                </button>
              </div>
            ))}
          </div>
        )}

        <div className="flex items-center space-x-3">
          <textarea
            value={replyMessage}
            onChange={(e) => setReplyMessage(e.target.value)}
            placeholder="Type your message..."
            rows="1"
            className="flex-1 px-4 py-3 pr-12 border border-gray-300 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white resize-none"
            onKeyDown={(e) => {
              if (e.key === "Enter" && !e.shiftKey) {
                e.preventDefault();
                replyPostMessage();
              }
            }}
          />

          <div className="flex items-center space-x-2">
            <button
              type="button"
              onClick={() => fileInputRef.current?.click()}
              className="p-2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 rounded-lg bg-gray-100 dark:bg-gray-800 hover:bg-gray-100 dark:hover:bg-gray-700 transition"
            >
              <PaperClipIcon className="w-5 h-5" />
            </button>
            <button
              type="button"
              onClick={replyPostMessage}
              className="p-3 bg-blue-600 hover:bg-blue-700 rounded-lg text-white transition shadow-md flex items-center justify-center"
            >
              <PaperAirplaneIcon className="w-5 h-5" />
            </button>
          </div>
        </div>

        <input
          ref={fileInputRef}
          type="file"
          multiple
          onChange={handleFileUpload}
          className="hidden"
        />
      </div>
    </div>
  );
}

export default SupportChat;
