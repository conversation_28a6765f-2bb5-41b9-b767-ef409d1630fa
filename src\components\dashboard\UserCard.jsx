"use client";
import { useUser } from "@/context/UserContext";
import React from "react";

function UserCard() {
  const { user, loading } = useUser();
  console.log("user", user);

  return (
    <>
      <div className="rounded-2xl border border-gray-200 bg-white px-8 py-8 item-center h-full">
        <h2 className="text-xl font-medium">
          <span className="text-blue-600 font-semibold">Hello,</span>
          <span className="text-sky-500">Good afternoon</span>
        </h2>
        <h1 className="text-4xl capitalize font-extrabold text-gray-800 mt-3">
          {user?.full_name}
        </h1>
        <div className="mt-5">
          <span className="inline-flex items-center gap-2 bg-gradient-to-r from-blue-500 to-sky-500 text-white px-3 py-1 rounded-lg text-sm font-medium">
            MID: 4983173934
          </span>
        </div>
        <p className="text-gray-500 text-sm mt-5">
          Last Login At <span className="italic">{user?.updated_at}</span> 
        </p>
        <p className="text-gray-500 text-sm mt-3">
           <span className="italic">Windows • Chrome</span> 
        </p>
      </div>
    </>
  );
}

export default UserCard;
