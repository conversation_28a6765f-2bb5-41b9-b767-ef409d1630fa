"use client";

import React, { useEffect, useState } from "react";
import <PERSON> from "next/link";
import {
  ArrowLeftIcon,
  PlusIcon,
  MagnifyingGlassIcon,
  FunnelIcon,
  EyeIcon,
  ChatBubbleLeftRightIcon,
  ClockIcon,
  CheckCircleIcon,
  ExclamationCircleIcon,
  XCircleIcon,
} from "@heroicons/react/24/outline";
import NetworkService from "@/network/service/network_service";
import ApiPath from "@/network/api/api_path";

function SupportTickets() {
  const [getTickets, setGetTickets] = useState({ tickets: [] });
  const [loading, setLoading] = useState(true);

  // use network
  const networkService = new NetworkService();

  // fetch tickets
  const fetchTickets = async () => {
    setLoading(true);
    try {
      const res = await networkService.get(ApiPath.getSupportTickets);
      if (res.status === "completed") {
        setGetTickets(res.data.data);
      }
    } catch (error) {
      console.error("Error fetching tickets:", error);
      setGetTickets({ tickets: [] });
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchTickets();
  }, []);

  // filters
  const [searchTerm, setSearchTerm] = useState("");
  const [statusFilter, setStatusFilter] = useState("all");
  const [priorityFilter, setPriorityFilter] = useState("all");

  // get status icon
  const getStatusIcon = (status) => {
    switch (status) {
      case "open":
        return <ExclamationCircleIcon className="w-4 h-4 text-blue-500" />;
      case "in_progress":
        return <ClockIcon className="w-4 h-4 text-yellow-500" />;
      case "waiting_response":
        return <ChatBubbleLeftRightIcon className="w-4 h-4 text-orange-500" />;
      case "resolved":
        return <CheckCircleIcon className="w-4 h-4 text-green-500" />;
      case "closed":
        return <XCircleIcon className="w-4 h-4 text-gray-500" />;
      default:
        return <ExclamationCircleIcon className="w-4 h-4 text-gray-500" />;
    }
  };

  // get status badge
  const getStatusBadge = (status) => {
    const statusConfig = {
      open: "bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200",
      in_progress:
        "bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200",
      waiting_response:
        "bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-200",
      resolved:
        "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200",
      closed: "bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200",
    };

    return (
      <span
        className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
          statusConfig[status] || statusConfig.closed
        }`}
      >
        {getStatusIcon(status)}
        <span className="ml-1 capitalize">{status.replace("_", " ")}</span>
      </span>
    );
  };

  // get priority badge
  const getPriorityBadge = (priority) => {
    const priorityConfig = {
      low: "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200",
      medium:
        "bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200",
      high: "bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-200",
      urgent: "bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200",
    };

    return (
      <span
        className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
          priorityConfig[priority] || priorityConfig.medium
        }`}
      >
        {priority.charAt(0).toUpperCase() + priority.slice(1)}
      </span>
    );
  };

  // Format date for better display
  const formatDate = (dateString) => {
    if (!dateString) return "N/A";
    const date = new Date(dateString);
    return isNaN(date)
      ? "Invalid date"
      : date.toLocaleDateString("en-US", {
          year: "numeric",
          month: "short",
          day: "numeric",
        });
  };

  // Filter tickets
  const filteredTickets = (getTickets?.tickets || []).filter((ticket) => {
    if (!ticket) return false;

    const matchesSearch =
      searchTerm === "" ||
      (ticket.title &&
        ticket.title.toLowerCase().includes(searchTerm.toLowerCase())) ||
      (ticket.id && ticket.id.toString().includes(searchTerm));

    const matchesStatus =
      statusFilter === "all" || ticket.status === statusFilter;

    const matchesPriority =
      priorityFilter === "all" || ticket.priority === priorityFilter;

    return matchesSearch && matchesStatus && matchesPriority;
  });

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <Link
            href="/support"
            className="inline-flex items-center text-sm text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200"
          >
            <ArrowLeftIcon className="w-4 h-4 mr-1" />
            Back to Support Center
          </Link>
        </div>
      </div>

      <div className="flex items-center justify-between">
        <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
          My Support Tickets
        </h1>
        <Link
          href="/support/create"
          className="inline-flex items-center px-4 py-2 bg-blue-600 text-white text-sm font-medium rounded-lg hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
        >
          <PlusIcon className="w-4 h-4 mr-2" />
          New Ticket
        </Link>
      </div>

      {/* Filters */}
      <div className="bg-white dark:bg-gray-800 shadow rounded-lg">
        <div className="p-6">
          <div className="flex flex-col sm:flex-row gap-4">
            {/* Search */}
            <div className="flex-1">
              <div className="relative">
                <MagnifyingGlassIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
                <input
                  type="text"
                  placeholder="Search tickets by title or ID..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="w-full pl-10 pr-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
                />
              </div>
            </div>

            {/* Status Filter */}
            <div className="sm:w-48">
              <select
                value={statusFilter}
                onChange={(e) => setStatusFilter(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
              >
                <option value="all">All Status</option>
                <option value="open">Open</option>
                <option value="in_progress">In Progress</option>
                <option value="waiting_response">Waiting Response</option>
                <option value="resolved">Resolved</option>
                <option value="closed">Closed</option>
              </select>
            </div>

            {/* Priority Filter */}
            <div className="sm:w-48">
              <select
                value={priorityFilter}
                onChange={(e) => setPriorityFilter(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
              >
                <option value="all">All Priority</option>
                <option value="low">Low</option>
                <option value="medium">Medium</option>
                <option value="high">High</option>
                <option value="urgent">Urgent</option>
              </select>
            </div>
          </div>
        </div>
      </div>

      {/* Tickets List */}
      <div className="bg-white dark:bg-gray-800 shadow rounded-lg">
        <div className="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
          <h2 className="text-lg font-medium text-gray-900 dark:text-white">
            Support Tickets ({filteredTickets.length})
          </h2>
        </div>

        <div className="overflow-hidden">
          {loading ? (
            <div className="p-6 text-center text-gray-500">
              <p className="mt-2">Loading tickets...</p>
            </div>
          ) : filteredTickets.length > 0 ? (
            <div className="divide-y divide-gray-200 dark:divide-gray-700">
              {filteredTickets.map((ticket) => (
                <div
                  key={ticket.id}
                  className="p-6 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"
                >
                  <div className="flex items-center justify-between">
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center space-x-3 mb-2">
                        <h3 className="text-sm font-medium text-gray-900 dark:text-white truncate">
                          {ticket.title || "Untitled Ticket"}
                        </h3>
                        <span className="text-xs text-gray-500 dark:text-gray-400">
                          #{ticket.id || "N/A"}
                        </span>
                      </div>

                      <div className="flex items-center space-x-4 mb-3">
                        {getStatusBadge(ticket.status || "closed")}
                        {getPriorityBadge(ticket.priority || "medium")}
                        <span className="text-xs text-gray-500 dark:text-gray-400">
                          {ticket.category || ""}
                        </span>
                      </div>

                      <div className="flex items-center space-x-4 text-xs text-gray-500 dark:text-gray-400">
                        <span>Created: {formatDate(ticket.created_at)}</span>
                        <span>Updated: {formatDate(ticket.updated_at)}</span>
                        <span className="flex items-center">
                          <ChatBubbleLeftRightIcon className="w-3 h-3 mr-1" />
                          {ticket.messages_count || 0} messages
                        </span>
                      </div>
                    </div>

                    <div className="flex items-center space-x-2 ml-4">
                      <Link
                        href={`/support/chat/${ticket.uuid}`}
                        className="inline-flex items-center px-3 py-1.5 border border-gray-300 dark:border-gray-600 text-xs font-medium rounded text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
                      >
                        <ChatBubbleLeftRightIcon className="w-3 h-3 mr-1" />
                        Chat
                      </Link>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <div className="p-12 text-center">
              <div className="mx-auto w-24 h-24 bg-gray-100 dark:bg-gray-700 rounded-full flex items-center justify-center mb-4">
                <FunnelIcon className="w-8 h-8 text-gray-400" />
              </div>
              <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
                {getTickets.tickets?.length > 0
                  ? "No matching tickets found"
                  : "No tickets found"}
              </h3>
              <p className="text-gray-500 dark:text-gray-400 mb-6">
                {getTickets.tickets?.length > 0
                  ? "No support tickets match your current filters."
                  : "You haven't created any support tickets yet."}
              </p>
              <Link
                href="/support/create"
                className="inline-flex items-center px-4 py-2 bg-blue-600 text-white text-sm font-medium rounded-lg hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
              >
                <PlusIcon className="w-4 h-4 mr-2" />
                Create New Ticket
              </Link>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}

export default SupportTickets;
